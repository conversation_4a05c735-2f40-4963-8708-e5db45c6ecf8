'use client'

import React, { useRef, useEffect, useState } from 'react'
import LightRays from '@/src/blocks/Backgrounds/LightRays/LightRays'
import Footer from '@/components/footer'
import ShinyText from '@/src/blocks/TextAnimations/ShinyText/ShinyText'
import { Separator } from '@/components/ui/separator'
import AnimatedContent from '@/src/blocks/Animations/AnimatedContent/AnimatedContent'             

export default function MainPage() {
  const contactRef = useRef<HTMLElement>(null)
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    setIsVisible(true)
  }, [])

  return (
    <div className="relative min-h-screen bg-transparent text-gray-200 flex flex-col font-sans overflow-hidden">
      {/* Enhanced Background */}
      <div className="fixed inset-0 -z-10 w-screen h-screen">
        <LightRays
          raysOrigin="top-center"
          raysColor="#000000"
          raysSpeed={1.5}
          lightSpread={0.8}
          rayLength={1.2}
          followMouse={true}
          mouseInfluence={0.1}
          noiseAmount={0.1}
          distortion={0.05}
          className="w-full h-full absolute top-0"
        />
        <div className="absolute inset-0 bg-gradient-to-b from-slate-900/80 via-slate-800/90 to-black" />
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,hsla(0,0%,100%,0.05)_0%,transparent_70%)]" />
        <div className="absolute inset-0 bg-[url('/path/to/subtle-texture.png')] opacity-5 mix-blend-soft-light" />
      </div>
      {/* Enhanced Main Content */}
      <main 
        className={`flex-grow flex flex-col items-center justify-center text-center px-6 py-24 transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}
      >
<div className="max-w-3xl">
  <h1 className="text-5xl md:text-7xl font-extrabold leading-tight mb-4">
    <div className="text-gray-300 mb-6">Summon Your Imagination</div>
    <div
      className="relative inline-block mt-1"
      style={{
        backgroundImage: `repeating-radial-gradient(circle at 0 0, rgba(0,0,0,0.03), rgba(0,0,0,0.03) 1px, transparent 2px, transparent 5px)`,
      }}
    >
    </div>
    <ShinyText
      text="Welcome to the Archive"
      disabled={false}
      speed={3}
      className="text-4xl md:text-6xl mt=1 font-bold custom-class "
    />
<Separator className="my-6 h-10 bg-gradient-to-r from-transparent via-gray-500 to-transparent" />
  </h1>
  <p className="mt-10 text-xl text-gray-400 max-w-2xl mx-auto">
    A thousand worlds. Infinite possibilities. Every character has a destiny — yours is to write it.
  </p>

          <div className="mt-16 flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-6 justify-center">
            <button className="px-10 py-4 border-2 border-slate-700 text-gray-300 font-semibold rounded-full bg-slate-900/40 backdrop-blur-sm hover:border-sky-500 hover:text-white transition-colors duration-300">
                Begin Your Journey
            </button>
            <button className="px-10 py-4 border-2 border-slate-700 text-gray-300 font-semibold rounded-full bg-slate-900/40 backdrop-blur-sm hover:border-sky-500 hover:text-white transition-colors duration-300">
              Explore Archives
            </button>
          </div>
        </div>

{/* Only animate when scrolled near (using Intersection Observer via AnimatedContent's threshold prop) */}
<AnimatedContent
  distance={80}
  direction="vertical"
  reverse={true}
  duration={1.4}
  ease="power1.out" // Changed from "bounce.out" to "power1.out" for a smooth slide
  initialOpacity={0}
  animateOpacity={true}
  scale={1.1}
  threshold={0.3}
  delay={0.3}
>
  {/* Floating Elements */}
  <div className="mt-40 grid grid-cols-2 md:grid-cols-4 gap-8 max-w-5xl mx-auto">
    {[1, 2, 3, 4].map((item) => (
      <div 
        key={item}
        className="bg-slate-900/40 backdrop-blur-sm border border-slate-700/50 rounded-xl p-6 hover:border-sky-500/50 transition-all duration-300 hover:-translate-y-2 cursor-pointer"
      >
        <div className="bg-slate-800 rounded-lg w-16 h-16 mx-auto mb-4 flex items-center justify-center">
          <div className="w-8 h-8 rounded-full bg-gradient-to-r from-sky-500 to-blue-500"></div>
        </div>
        <h3 className="text-lg font-bold text-gray-300 mb-2">Realm {item}</h3>
        <p className="text-gray-500 text-sm">Unwritten stories await</p>
      </div>
    ))}
  </div>
</AnimatedContent>
      </main>

      {/* Footer */}
      <div className={`transition-opacity duration-1000 ${isVisible ? 'opacity-100' : 'opacity-0'}`}>
        <Footer contactRef={contactRef} />
      </div>
    </div>
  )
}