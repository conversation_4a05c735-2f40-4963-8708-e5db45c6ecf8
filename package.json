{"name": "character-archive", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "gsap": "^3.13.0", "lucide-react": "^0.533.0", "next": "15.4.4", "next-themes": "^0.4.6", "ogl": "^1.0.11", "react": "19.1.0", "react-dom": "19.1.0", "react-icons": "^5.5.0", "separator": "^0.1.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.4.4", "postcss": "^8.5.6", "shadcn": "^2.9.3", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.6", "typescript": "^5"}}